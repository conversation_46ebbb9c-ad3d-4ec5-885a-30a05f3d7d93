package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

type Template struct {
	TemplateID          uuid.UUID      `gorm:"type:uuid;primaryKey;default:uuid_generate_v4()" json:"template_id"`
	TemplateName        string         `gorm:"type:varchar(255);not null" json:"template_name"`
	TemplateDescription *string        `gorm:"type:text" json:"template_description,omitempty"`
	IsOfficial          bool           `gorm:"not null;default:false" json:"is_official"`
	IsPublic            bool           `gorm:"not null;default:false" json:"is_public"`
	CreatorUserID       *uuid.UUID     `gorm:"type:uuid" json:"creator_user_id,omitempty"`
	PreviewImageURL     *string        `gorm:"type:varchar(255)" json:"preview_image_url,omitempty"`
	TemplateData        datatypes.JSON `gorm:"type:jsonb;not null" json:"template_data"`
	CreatedAt           time.Time      `gorm:"type:timestamptz;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt           time.Time      `gorm:"type:timestamptz;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Foreign key relationships
	Creator *User `gorm:"foreignKey:CreatorUserID;references:UserID" json:"creator,omitempty"`
}

// TableName specifies the table name for GORM
func (Template) TableName() string {
	return "templates"
}
