package main

import (
	"bufio"
	"fmt"
	"link-nest/configs"
	"os"
	"strings"
	"syscall"

	"golang.org/x/term"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// Load configuration
	cfg := configs.LoadConfig()
	
	fmt.Printf("Current database configuration:\n")
	fmt.Printf("Host: %s\n", cfg.Database.Host)
	fmt.Printf("Port: %s\n", cfg.Database.Port)
	fmt.Printf("User: %s\n", cfg.Database.User)
	fmt.Printf("Database: %s\n", cfg.Database.DBName)
	fmt.Printf("Current password: %s\n", cfg.Database.Password)
	
	reader := bufio.NewReader(os.Stdin)
	
	// Ask for username
	fmt.Print("\nEnter username (press Enter to use current): ")
	username, _ := reader.ReadString('\n')
	username = strings.TrimSpace(username)
	if username == "" {
		username = cfg.Database.User
	}
	
	// Ask for database name
	fmt.Print("Enter database name (press Enter to use current): ")
	dbname, _ := reader.ReadString('\n')
	dbname = strings.TrimSpace(dbname)
	if dbname == "" {
		dbname = cfg.Database.DBName
	}
	
	// Ask for password securely
	fmt.Print("Enter password: ")
	passwordBytes, err := term.ReadPassword(int(syscall.Stdin))
	if err != nil {
		fmt.Printf("Error reading password: %v\n", err)
		return
	}
	password := string(passwordBytes)
	fmt.Println() // New line after password input
	
	// Test connection
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
		cfg.Database.Host,
		username,
		password,
		dbname,
		cfg.Database.Port,
		cfg.Database.SSLMode,
	)
	
	fmt.Printf("\nTesting connection with:\n")
	fmt.Printf("User: %s\n", username)
	fmt.Printf("Database: %s\n", dbname)
	fmt.Printf("Password: %s\n", strings.Repeat("*", len(password)))
	
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	
	if err != nil {
		fmt.Printf("❌ Connection failed: %v\n", err)
		
		// Try with postgres database
		if dbname != "postgres" {
			fmt.Println("\nTrying with 'postgres' database...")
			dsnPostgres := fmt.Sprintf("host=%s user=%s password=%s dbname=postgres port=%s sslmode=%s",
				cfg.Database.Host,
				username,
				password,
				cfg.Database.Port,
				cfg.Database.SSLMode,
			)
			
			dbPostgres, errPostgres := gorm.Open(postgres.Open(dsnPostgres), &gorm.Config{
				Logger: logger.Default.LogMode(logger.Info),
			})
			
			if errPostgres != nil {
				fmt.Printf("❌ Connection to postgres database also failed: %v\n", errPostgres)
			} else {
				fmt.Printf("✅ Successfully connected to postgres database!\n")
				
				// List available databases
				var databases []string
				err = dbPostgres.Raw("SELECT datname FROM pg_database WHERE datistemplate = false").Scan(&databases).Error
				if err != nil {
					fmt.Printf("❌ Failed to list databases: %v\n", err)
				} else {
					fmt.Printf("📋 Available databases: %v\n", databases)
				}
				
				// Check if target database exists
				var exists bool
				err = dbPostgres.Raw("SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = ?)", dbname).Scan(&exists).Error
				if err != nil {
					fmt.Printf("❌ Failed to check if database exists: %v\n", err)
				} else if !exists {
					fmt.Printf("⚠️  Database '%s' does not exist!\n", dbname)
					fmt.Printf("💡 You may need to create it first: CREATE DATABASE %s;\n", dbname)
				} else {
					fmt.Printf("✅ Database '%s' exists\n", dbname)
				}
				
				sqlDB, _ := dbPostgres.DB()
				sqlDB.Close()
			}
		}
		return
	}
	
	fmt.Printf("✅ SUCCESS: Connected to database!\n")
	
	// Test the connection
	sqlDB, err := db.DB()
	if err != nil {
		fmt.Printf("❌ Failed to get sql.DB: %v\n", err)
		return
	}
	
	err = sqlDB.Ping()
	if err != nil {
		fmt.Printf("❌ Failed to ping: %v\n", err)
		return
	}
	
	fmt.Printf("✅ Database ping successful!\n")
	
	// Get database version
	var version string
	err = db.Raw("SELECT version()").Scan(&version).Error
	if err != nil {
		fmt.Printf("❌ Failed to get version: %v\n", err)
	} else {
		fmt.Printf("📊 Database version: %s\n", version[:100]+"...")
	}
	
	sqlDB.Close()
	
	// Update config file if successful
	fmt.Print("\nDo you want to update the config file with these credentials? (y/N): ")
	response, _ := reader.ReadString('\n')
	response = strings.TrimSpace(strings.ToLower(response))
	
	if response == "y" || response == "yes" {
		// Update the config file
		newConfig := fmt.Sprintf(`server:
  port: ":8080"
database:
  host: "%s"
  port: "%s"
  user: "%s"
  password: "%s"
  dbname: "%s"
  sslmode: "%s"`,
			cfg.Database.Host,
			cfg.Database.Port,
			username,
			password,
			dbname,
			cfg.Database.SSLMode,
		)
		
		err = os.WriteFile("configs/config.yaml", []byte(newConfig), 0644)
		if err != nil {
			fmt.Printf("❌ Failed to update config file: %v\n", err)
		} else {
			fmt.Printf("✅ Config file updated successfully!\n")
		}
	}
}
