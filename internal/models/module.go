package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

type Module struct {
	ModuleID      uuid.UUID      `gorm:"type:uuid;primaryKey;default:uuid_generate_v4()" json:"module_id"`
	PageID        uuid.UUID      `gorm:"type:uuid;not null" json:"page_id"`
	ModuleType    string         `gorm:"type:varchar(50);not null" json:"module_type"`
	ModuleTitle   *string        `gorm:"type:varchar(255)" json:"module_title,omitempty"`
	ModuleIconURL *string        `gorm:"type:varchar(255)" json:"module_icon_url,omitempty"`
	ModuleOrder   int            `gorm:"not null" json:"module_order"`
	IsEnabled     bool           `gorm:"not null;default:true" json:"is_enabled"`
	Config        datatypes.JSON `gorm:"type:jsonb;not null" json:"config"`
	CreatedAt     time.Time      `gorm:"type:timestamptz;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt     time.Time      `gorm:"type:timestamptz;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Foreign key relationships
	Page UserPage `gorm:"foreignKey:PageID;references:PageID" json:"page"`
}

// TableName specifies the table name for GORM
func (Module) TableName() string {
	return "modules"
}
